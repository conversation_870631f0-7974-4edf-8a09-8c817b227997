'use client';

import { useSupabase } from '@/features/shared/components/SupabaseProvider';
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { useCallback, useEffect, useRef } from 'react';
import {
  AutoAssignmentRule,
  DefaultAgentSettings,
  UserSettings,
} from '../models/settings.schema';
import { useSettingsStore } from '../store/use-settings-store';
import { SettingsCacheService } from './settings-cache.service';

// Database row types for settings tables
type UserSettingsRow = UserSettings;
type DefaultAgentSettingsRow = DefaultAgentSettings;
type AutoAssignmentRuleRow = AutoAssignmentRule;

// Union type for all settings table rows
type SettingsTableRow =
  | UserSettingsRow
  | DefaultAgentSettingsRow
  | AutoAssignmentRuleRow;

interface SettingsRealtimeEvent {
  table: string;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: SettingsTableRow;
  old?: SettingsTableRow;
}

/**
 * Real-time settings synchronization service
 * Listens for changes to settings tables and updates local state/cache
 */
export function useSettingsRealtime(tenantId: string, userId: string) {
  const { supabase } = useSupabase();
  const { loadSettings } = useSettingsStore();
  const cacheService = SettingsCacheService.getInstance();
  const channelRef = useRef<{ unsubscribe: () => void } | null>(null);
  const lastSyncRef = useRef<number>(0);

  // Debounced sync to prevent excessive updates
  const debouncedSync = useCallback(async () => {
    const now = Date.now();
    if (now - lastSyncRef.current < 3000) return; // Minimum 3 seconds between syncs to prevent conflicts with component save operations

    lastSyncRef.current = now;
    await loadSettings(tenantId, userId);
  }, [tenantId, userId, loadSettings]);

  // Handle user settings changes
  const handleUserSettingsChange = useCallback(
    async (payload: RealtimePostgresChangesPayload<UserSettingsRow>) => {
      if (
        !payload.new ||
        !('tenant_id' in payload.new) ||
        payload.new.tenant_id !== tenantId
      )
        return;

      try {
        // Update local state optimistically
        if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
          const updatedSettings = payload.new as UserSettings;

          // Only update if this is for the current user
          if (updatedSettings.user_id === userId) {
            useSettingsStore.setState({
              userSettings: updatedSettings,
              lastSync: Date.now(),
            });

            // Update cache
            await cacheService.updateUserSettingsInCache(
              tenantId,
              userId,
              updatedSettings
            );
          }
        } else if (payload.eventType === 'DELETE') {
          // Handle deletion (rare case)
          debouncedSync();
        }
      } catch (error) {
        console.warn('Failed to handle user settings realtime update:', error);
        // Fallback to full sync
        debouncedSync();
      }
    },
    [tenantId, userId, cacheService, debouncedSync]
  );

  // Handle admin settings changes
  const handleAdminSettingsChange = useCallback(
    async (
      payload: RealtimePostgresChangesPayload<
        DefaultAgentSettingsRow | AutoAssignmentRuleRow
      >
    ) => {
      if (
        !payload.new ||
        !('tenant_id' in payload.new) ||
        payload.new.tenant_id !== tenantId
      )
        return;

      try {
        // For admin settings, we need to refresh the full admin settings
        // since they involve multiple tables (default_agent_settings, auto_assignment_rules)
        debouncedSync();
      } catch (error) {
        console.warn('Failed to handle admin settings realtime update:', error);
        debouncedSync();
      }
    },
    [tenantId, debouncedSync]
  );

  // Set up real-time subscriptions
  useEffect(() => {
    if (!supabase || !tenantId || !userId) return;

    const channelName = `settings_${tenantId}_${userId}`;

    // Clean up existing channel
    if (channelRef.current) {
      channelRef.current.unsubscribe();
    }

    // Create new channel
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_settings',
          filter: `tenant_id=eq.${tenantId}`,
        },
        handleUserSettingsChange
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'default_agent_settings',
          filter: `tenant_id=eq.${tenantId}`,
        },
        handleAdminSettingsChange
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'auto_assignment_rules',
          filter: `tenant_id=eq.${tenantId}`,
        },
        handleAdminSettingsChange
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Settings realtime subscription active');
        } else if (status === 'CHANNEL_ERROR') {
          console.warn('Settings realtime subscription error');
        }
      });

    channelRef.current = channel;

    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
        channelRef.current = null;
      }
    };
  }, [
    supabase,
    tenantId,
    userId,
    handleUserSettingsChange,
    handleAdminSettingsChange,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
    };
  }, []);

  return {
    isConnected: !!channelRef.current,
    lastSync: lastSyncRef.current,
  };
}

/**
 * Settings broadcast service for notifying other tabs/windows
 */
export class SettingsBroadcastService {
  private static instance: SettingsBroadcastService;
  private channel: BroadcastChannel | null = null;
  private listeners: Set<(event: SettingsRealtimeEvent) => void> = new Set();

  private constructor() {
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      this.channel = new BroadcastChannel('settings_sync');
      this.channel.addEventListener('message', this.handleMessage.bind(this));
    }
  }

  static getInstance(): SettingsBroadcastService {
    if (!SettingsBroadcastService.instance) {
      SettingsBroadcastService.instance = new SettingsBroadcastService();
    }
    return SettingsBroadcastService.instance;
  }

  private handleMessage(event: MessageEvent<SettingsRealtimeEvent>) {
    this.listeners.forEach((listener) => {
      try {
        listener(event.data);
      } catch (error) {
        console.warn('Settings broadcast listener error:', error);
      }
    });
  }

  broadcast(event: SettingsRealtimeEvent) {
    if (this.channel) {
      this.channel.postMessage(event);
    }
  }

  subscribe(listener: (event: SettingsRealtimeEvent) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  destroy() {
    if (this.channel) {
      this.channel.close();
      this.channel = null;
    }
    this.listeners.clear();
  }
}

/**
 * Hook for cross-tab settings synchronization
 */
export function useSettingsBroadcast(tenantId: string, userId: string) {
  const { loadSettings } = useSettingsStore();
  const broadcastService = SettingsBroadcastService.getInstance();

  useEffect(() => {
    const unsubscribe = broadcastService.subscribe(async (event) => {
      // Reload settings when changes are broadcast from other tabs
      if (
        event.table.includes('settings') ||
        event.table.includes('assignment')
      ) {
        await loadSettings(tenantId, userId);
      }
    });

    return unsubscribe;
  }, [tenantId, userId, loadSettings, broadcastService]);

  const broadcastChange = useCallback(
    (table: string, eventType: 'INSERT' | 'UPDATE' | 'DELETE') => {
      broadcastService.broadcast({
        table,
        eventType,
      });
    },
    [broadcastService]
  );

  return { broadcastChange };
}
